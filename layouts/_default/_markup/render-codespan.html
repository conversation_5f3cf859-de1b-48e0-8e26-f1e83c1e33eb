{{- $text := .Text -}}
{{- $isQQEmoji := false -}}

{{- /* Check if this is a QQ emoji pattern :/emoji_name: */ -}}
{{- if and (hasPrefix $text ":/") (hasSuffix $text ":") (gt (len $text) 3) -}}
    {{- $emojiName := substr $text 2 (sub (len $text) 3) -}}
    {{- $isQQEmoji = true -}}

    {{- /* Render QQ emoji with dynamic loading */ -}}
    <span class="qq-emoji-container" data-emoji-name="{{ $emojiName }}" title=":/{{ $emojiName }}:">
        <span class="qq-emoji-placeholder">:/{{ $emojiName }}:</span>
    </span>
{{- end -}}

{{- /* If not a QQ emoji, render as normal code span */ -}}
{{- if not $isQQEmoji -}}
<code>{{ .Text }}</code>
{{- end -}}
