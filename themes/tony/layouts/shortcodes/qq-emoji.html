{{/* QQ表情单个表情shortcode */}}
{{/* 用法: {{< qq-emoji "微笑" >}} */}}

{{- $emojiName := .Get 0 -}}
{{- if not $emojiName -}}
    <span class="qq-emoji-error">❌ 请提供表情名称</span>
{{- else -}}
    {{/* 从data文件中查找表情 */}}
    {{- $emojiData := dict -}}
    {{- $found := false -}}

{{- $data := index .Site.Data "qq_emoji_mapping" -}}
    {{/* 调试信息 */}}
    {{- if not $data -}}
        <span class="qq-emoji-error">❌ 数据文件未加载</span>
    {{- else -}}
        <!-- DEBUG: Data loaded, length: {{ len $data }}, searching for: /{{ $emojiName }} -->
        {{- $targetDescribe := printf "/%s" $emojiName -}}
        {{- range $data -}}
            {{- if eq .describe $targetDescribe -}}
                {{- $emojiData = . -}}
                {{- $found = true -}}
            {{- end -}}
        {{- end -}}
    {{- end -}}

    {{- if $found -}}
        {{- if eq $emojiData.emojiType 1 -}}
            {{/* 超级表情，使用Lottie动画 */}}
            {{- $lottieAsset := "" -}}
            {{- range $emojiData.assets -}}
                {{- if eq .type 3 -}}
                    {{- $lottieAsset = .path -}}
                {{- end -}}
            {{- end -}}
            {{- if $lottieAsset -}}
                {{- $uniqueId := printf "lottie-emoji-%s-%d" $emojiData.emojiId now.UnixNano -}}
                <div id="{{ $uniqueId }}" class="super-qmoji" data-lottie-path="https://koishi.js.org/QFace/{{ $lottieAsset }}" title="{{ $emojiData.describe }}" style="width: 32px; height: 32px; display: inline-block;"></div>
            {{- else -}}
                <span class="qq-emoji-fallback">{{ $emojiData.describe }}</span>
            {{- end -}}
        {{- else -}}
            {{/* 普通表情，使用APNG */}}
            {{- $apngAsset := "" -}}
            {{- range $emojiData.assets -}}
                {{- if eq .type 2 -}}
                    {{- $apngAsset = .path -}}
                {{- end -}}
            {{- end -}}
            {{- if $apngAsset -}}
                <img src="https://koishi.js.org/QFace/{{ $apngAsset }}" class="qmoji" alt="{{ $emojiData.describe }}" title="{{ $emojiData.describe }}" style="width: 32px; height: 32px; display: inline-block;" />
            {{- else -}}
                <span class="qq-emoji-fallback">{{ $emojiData.describe }}</span>
            {{- end -}}
        {{- end -}}
    {{- else -}}
        <span class="qq-emoji-fallback">:/{{ $emojiName }}:</span>
    {{- end -}}
{{- end -}}
