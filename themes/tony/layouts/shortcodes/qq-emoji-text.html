{{/* QQ表情文本批量处理shortcode */}}
{{/* 用法: {{< qq-emoji-text >}}这是一段包含:/微笑:表情的文本:/大哭:{{< /qq-emoji-text >}} */}}

{{- $content := .Inner -}}
{{- $processedContent := $content -}}

{{/* 创建表情映射字典 */}}
{{- $emojiMap := dict -}}
{{- $data := index .Site.Data "qq_emoji_mapping" -}}
{{- range $data -}}
    {{- $key := strings.TrimPrefix "/" .describe -}}
    {{- $emojiMap = $emojiMap | merge (dict $key .) -}}
{{- end -}}

{{/* 查找所有 :/表情名: 模式 */}}
{{- $patterns := findRE `:/([^:/\s]+):` $processedContent -}}
{{- $patterns = $patterns | uniq -}}

{{/* 处理每个找到的表情 */}}
{{- range $patterns -}}
    {{- $emojiName := replaceRE `:/([^:/\s]+):` "$1" . -}}
    {{- $emojiData := index $emojiMap $emojiName -}}
    
    {{- if $emojiData -}}
        {{- if eq $emojiData.emojiType 1 -}}
            {{/* 超级表情，使用Lottie动画 */}}
            {{- $lottieAsset := "" -}}
            {{- range $emojiData.assets -}}
                {{- if eq .type 3 -}}
                    {{- $lottieAsset = .path -}}
                {{- end -}}
            {{- end -}}
            {{- if $lottieAsset -}}
                {{- $uniqueId := printf "lottie-emoji-%s-%d" $emojiData.emojiId now.UnixNano -}}
                {{- $replacement := printf `<div id="%s" class="super-qmoji" data-lottie-path="https://koishi.js.org/QFace/%s" title="%s" style="width: 32px; height: 32px; display: inline-block;"></div>` $uniqueId $lottieAsset $emojiData.describe -}}
                {{- $processedContent = replace $processedContent . $replacement -}}
            {{- else -}}
                {{- $replacement := printf `<span class="qq-emoji-fallback">%s</span>` $emojiData.describe -}}
                {{- $processedContent = replace $processedContent . $replacement -}}
            {{- end -}}
        {{- else -}}
            {{/* 普通表情，使用APNG */}}
            {{- $apngAsset := "" -}}
            {{- range $emojiData.assets -}}
                {{- if eq .type 2 -}}
                    {{- $apngAsset = .path -}}
                {{- end -}}
            {{- end -}}
            {{- if $apngAsset -}}
                {{- $replacement := printf `<img src="https://koishi.js.org/QFace/%s" class="qmoji" alt="%s" title="%s" style="width: 32px; height: 32px; display: inline-block;" />` $apngAsset $emojiData.describe $emojiData.describe -}}
                {{- $processedContent = replace $processedContent . $replacement -}}
            {{- else -}}
                {{- $replacement := printf `<span class="qq-emoji-fallback">%s</span>` $emojiData.describe -}}
                {{- $processedContent = replace $processedContent . $replacement -}}
            {{- end -}}
        {{- end -}}
    {{- else -}}
        {{/* 未找到表情，显示为fallback */}}
        {{- $replacement := printf `<span class="qq-emoji-fallback">%s</span>` . -}}
        {{- $processedContent = replace $processedContent . $replacement -}}
    {{- end -}}
{{- end -}}

{{/* 输出处理后的内容 */}}
{{ $processedContent | safeHTML }}
