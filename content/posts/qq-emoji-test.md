---
title: "QQ表情测试页面"
date: 2024-01-01T00:00:00Z
draft: false
tags: ["测试", "QQ表情"]
categories: ["技术"]
---

# QQ表情功能测试

这个页面用于测试新实现的QQ表情解析功能。

## 普通QQ表情 (Normal Qmoji)

以下是一些普通的QQ表情：

- 微笑：:/微笑:
- 撇嘴：:/撇嘴:
- 色：:/色:
- 发呆：:/发呆:
- 得意：:/得意:
- 大哭：:/大哭:
- 害羞：:/害羞:
- 闭嘴：:/闭嘴:
- 睡：:/睡:
- 尴尬：:/尴尬:

## Super QQ表情 (Super Qmoji)

以下是一些带动画的Super表情：

- 流泪：:/流泪:
- 蛋糕：:/蛋糕:
- 太阳：:/太阳:
- 月亮：:/月亮:

## 混合文本测试

这是一段包含表情的文本：今天天气真好:/微笑:，我很开心:/得意:！但是想到明天要上班就:/大哭:了。

## 不存在的表情测试

这个表情不存在：:/不存在的表情:，应该显示为fallback样式。

## 表情在不同上下文中的测试

### 在列表中
1. 第一项 :/微笑:
2. 第二项 :/流泪:
3. 第三项 :/太阳:

### 在引用中
> 这是一个引用，包含表情 :/得意:

### 在代码块中
```
这里的表情不应该被解析：:/微笑:
```

但是行内代码中的表情不会被解析：`:/害羞:`，而普通文本中的会被解析：:/害羞:

---

如果表情正确显示，说明QQ表情解析功能工作正常！
